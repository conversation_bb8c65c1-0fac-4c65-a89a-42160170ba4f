import React, { useState, useEffect } from 'react';
import { Modal, Button, Form } from 'react-bootstrap';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import FilterLocation from '../pages/FilterLocation';
import { TextField } from '@mui/material';

import moment from 'moment';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { format } from 'date-fns';

function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`incident-tabpanel-${index}`}
            aria-labelledby={`incident-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `incident-tab-${index}`,
        'aria-controls': `incident-tabpanel-${index}`,
    };
}

const ReportModal = ({ type, formData, setFormData }) => {

    const [value, setValue] = useState(0);
    const [topLevelValue, setTopLevelValue] = useState(0);
    const [maxDate, setMaxDate] = useState(new Date());
    const currentDate = new Date();
    const firstDayOfPreviousMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const [monthYear, setMonthYear] = useState(firstDayOfPreviousMonth);



    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const handleTopLevelChange = (event, newValue) => {
        setTopLevelValue(newValue);
    };

    useEffect(() => {
        // Get the current date
        const currentDate = new Date();

        // Set the date to the first day of the current month
        const firstDayOfCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

        // Subtract one day to get the last day of the previous month
        const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
        lastDayOfPreviousMonth.setDate(0);

        // Set maxDate to the last day of the previous month
        setMaxDate(lastDayOfPreviousMonth);
    }, []);

    const [location, setLocation] = useState({
        locationOneId: '', locationTwoId: '', locationThreeId: '', locationFourId: ''
    })
    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        setFormData(prevFormData => ({
            ...prevFormData,
            locationOneId: locationOneId,
            locationTwoId: locationTwoId,
            locationThreeId: locationThreeId,
            locationFourId: locationFourId
        }))
        setLocation({ locationOneId: locationOneId, locationTwoId: locationTwoId, locationThreeId: locationThreeId, locationFourId: locationFourId })
    };

    const handleInputChange = (event) => {
        const { id, value } = event.target;
        if (id === 'dateInput') {
            // For the date input, convert and update the state as 'DD/MM/YYYY'
            const formattedDate = moment(value).format('DD/MM/YYYY');
            setFormData(prevFormData => ({
                ...prevFormData,
                date: formattedDate
            }));
        } else {
            // For other inputs, update the state directly
            setFormData(prevFormData => ({
                ...prevFormData,
                [id]: value
            }));
        }
    };

    const handleDateChange = (newDate) => {
        setFormData(prevFormData => ({
            ...prevFormData,
            date: moment(newDate).format('DD/MM/YYYY')
        }));
    };

    // Initialize with an empty string or current month-year

    const handleMonthYearChange = (date) => {
        // Convert YYYY-MM to MM/YY

        setFormData(prevFormData => ({
            ...prevFormData,
            yearAndMonth: format(date, 'MMM yyyy')
        }))
        setMonthYear(date);
    };

    const handleWheel = (event) => {
      
        event.target.blur();  // Prevents scrolling changing the number
    };

    return (
        <>
            <Box sx={{ width: '100%' }}>

                {type === 'daily' && <form>

                    <FilterLocation disableAll={true} handleFilter={handleFilter} />

                    <div className="mb-3">
                        <label htmlFor="dateInput" className="form-label">Date *</label>
                        <input
                            type="date"
                            className="form-control"
                            id="dateInput"
                            value={moment(formData.date, 'DD/MM/YYYY').isValid() ? moment(formData.date, 'DD/MM/YYYY').format('YYYY-MM-DD') : ''}
                            onChange={handleInputChange}
                        />
                    </div>



                </form>}

                {type === 'monthly' && <form>






                  

                    {/* Below are the safety related fields */}


                    <div className="mb-3">
                        <label htmlFor="sttEmployeesInput" className="form-label">Number of STT GDC Employees on-site *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="numberOfEmployees"
                            value={formData.numberOfEmployees}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="dailyHoursInput" className="form-label">Total number of hours worked of STT GDC employee *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="dailyHoursOfEmployee"
                            value={formData.dailyHoursOfEmployee}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="workingDaysOfEmployee" className="form-label">Number of working days for STT GDC employee *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="workingDaysOfEmployee"
                            value={formData.workingDaysOfEmployee}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>



                    <div className="mb-3">
                        <label htmlFor="contractorEmployeesInput" className="form-label">Number of Contractor Employees on-site (incl. GC/Main Contractor, Sub-contractors, etc.) *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="numberofContractors"
                            value={formData.numberofContractors}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="dailyHoursContractorInput" className="form-label">Total No of hours worked of contractor employee *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="dailyHoursOfContractors"
                            value={formData.dailyHoursOfContractors}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="workingDaysOfContractors" className="form-label">Number of working days for Contractors *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="workingDaysOfContractors"
                            value={formData.workingDaysOfContractors}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfSafety" className="form-label">No. of Safety Inductions Conducted *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfSafety"
                            value={formData.noOfSafety}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfToolbox" className="form-label">No. of Toolbox Meetings/Safety Briefings/Safe Starts</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfToolbox"
                            value={formData.noOfToolbox}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfEhsTraining" className="form-label">No. of EHS Trainings *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfEhsTraining"
                            value={formData.noOfEhsTraining}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfInspection" className="form-label">No. of EHS Inspections/Audits *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfInspection"
                            value={formData.noOfInspection}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfManagmentSiteWalk" className="form-label">No. of Management Site Walk/Inspection *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfManagmentSiteWalk"
                            value={formData.noOfManagmentSiteWalk}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>

                    <div className="mb-3">
                        <label htmlFor="noOfAuthority" className="form-label">No. of Authority/NGO/Union Visits *</label>
                        <input
                            type="number"
                            className="form-control"
                            id="noOfAuthority"
                            value={formData.noOfAuthority}
                            onChange={handleInputChange}
                            onWheel={handleWheel}
                        />
                    </div>



                </form>
                }

            </Box>
        </>
    )
}

export default ReportModal;
