import React, { Component, useState, useEffect } from 'react'
import API from '../services/API'
import cogoToast from 'cogo-toast';
import moment from 'moment';
import AllFilterLocationVertical from './AllFilterLocationVertical'
import Box from '@mui/material/Box';
import { AUDIT_WITH_ID_URL, INSPECTION_URL, INSPECTION_WITH_ID_URL, AUDIT_URL } from '../constants';
import AuditData from './AuditData';

const Audit = () => {
  const [dates, setDates] = useState([])
  const [clear, setClear] = useState(true)
  const [filter, setFilter] = useState([])
  const [showFilter, setShowFilter] = useState(true)
  const [applyFilter, setApplyFilter] = useState(false)
  const [locationOneId, setlocationOneId] = useState('')
  const [locationTwoId, setlocationTwoId] = useState('')
  const [locationThreeId, setlocationThreeId] = useState('')
  const [locationFourId, setlocationFourId] = useState('')
  const [search, setSearch] = useState('')

  const [data, setData] = useState([]);
  const [filterData, setFilterData] = useState([]);
  useEffect(() => {
    getObservationData();
  }, [])

  const getObservationData = async () => {

    const params = {
      "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]
    };



    const response = await API.get((`${AUDIT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`));
    if (response.status === 200) {
      setData(response.data)
      setFilterData(response.data)
    }
  }

  const deleteObservationReport = async (id) => {


    const response = await API.delete(`${AUDIT_WITH_ID_URL(id)}`);

    if (response.status === 204) {

      cogoToast.success('Deleted!')
      getObservationData()

    }

  }

  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {
    setDates([])
    const data = [
      locationOneId.name || '',
      locationTwoId.name || '',
      locationThreeId.name || '',
      locationFourId.name || '',

    ];

    if (startDate !== null && endDate !== null) {
      const date = [
        moment(startDate).format('MMM YYYY'),
        moment(endDate).format('MMM YYYY')
      ]
      setDates(date)
    }


    setFilter(data)
    setSearch('')

    setlocationOneId(locationOneId.id)
    setlocationTwoId(locationTwoId.id)
    setlocationThreeId(locationThreeId.id)
    setlocationFourId(locationFourId.id)
    // setStartDate(startDate)
    // setEndDate(endDate)
  };

  useEffect(() => {

    const filterData1 = (data) => {
      return data.filter(item => {
        return (
          (!locationOneId || item.locationOneId === locationOneId) &&
          (!locationTwoId || item.locationTwoId === locationTwoId) &&
          (!locationThreeId || item.locationThreeId === locationThreeId) &&
          (!locationFourId || item.locationFourId === locationFourId)
          //   (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))

        );
      });
    };

    setFilterData(filterData1(data));

  }, [locationOneId, locationTwoId, locationThreeId, locationFourId])

  const onApplyFilter = (type) => {
    setApplyFilter(type)
    setShowFilter(true)
  }

  const onCancelFilter = (type) => {
    setApplyFilter(false)
    setShowFilter(true)
    setClear(!clear)
  }

  return (
    <div>
      <h3 className='mt-5'> Audit Report Details</h3>
      {/* <ThemeProvider theme={defaultMaterialTheme}>
            <MaterialTable
              columns={observationColumns}
              data={filterData}
              title=""
              style={tableStyle}
              actions={tableActions}
              options={{ pageSize: 20, ...tableOptions }}
              localization={localization}
            />
          </ThemeProvider> */}
      <div className='row'>
        <div className='col-12 mb-4'>
          <div className='d-flex align-items-center'>
            <div className='col-1 d-flex'>
              {!applyFilter ?
                <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                  <div className='d-flex flex-column align-items-end'>
                    <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                    <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                  </div>
                  <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                </div>
                :
                <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                  <div className='d-flex flex-column align-items-end'>
                    <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                    <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                  </div>
                  <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                </div>
              }

            </div>
            <div className='col-9 d-flex'>
              {applyFilter && <>
                {filter.length !== 0 &&
                  <h5><b>Location : </b>{filter.map((location, index) => (
                    location !== '' &&
                    <React.Fragment key={index}>
                      <span className='loc-box'>{location}</span>
                      {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                    </React.Fragment>
                  ))}</h5>
                }
                {dates.length !== 0 &&
                  <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                    <React.Fragment key={index}>
                      <span className='loc-box'>{location}</span>
                      {index < dates.length - 1 && " To "}
                    </React.Fragment>
                  ))}</h5>
                }
              </>}
            </div>

          </div>
        </div>
        <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>

          <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
        </div>
        <div className={!showFilter ? 'col-9' : 'col-12'}>
          <Box sx={{ width: '100%' }}>
            <AuditData setTotalOtherObservation={'0'} obsdata={filterData} clear={clear} search={search} />
          </Box>
        </div>
      </div>
    </div>
  )
}


export default Audit
