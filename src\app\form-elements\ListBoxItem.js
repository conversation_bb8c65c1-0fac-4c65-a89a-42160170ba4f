// @ts-nocheck
import React, { useRef, useState } from "react";
import { Dropdown } from 'react-bootstrap';
import { useHistory } from "react-router-dom";
import FlagButton from "../pages/FlagButton";
const ListBoxItem = (props) => {
    const history = useHistory();
    const nameRef = useRef(null)

    const [active, setActive] = useState(props.data.flag ? props.data.flag : false)

    const handleNameUpdate = () => {
        const newName = nameRef.current.textContent.trim();

        props.onHandleEditItem(props.data.id, newName)


    };


    const handleFlagUpdate = (flag) => {

        setActive(flag)
        props.onHandleEditFlag(props.data.id, flag)


    };



    return (
        <>

            <div className={`list d-flex justify-content-between align-items-center p-3 border-bottom cursor-pointer ${(props.selectedId === props.data.id ? "active" : '')}`} onClick={() => props.onHandleSelect(props.data.id)}>

                <div className="content">
                    <p
                        className="content-editable list-name m-0"
                        ref={nameRef}
                        contentEditable="true"
                        onBlur={handleNameUpdate}
                        autoComplete="off"
                    >
                        {props.data.name}
                        {props.tooltip && props.data.tooltips && (
                            <i className="mdi mdi-information-outline ms-2 text-info" title={props.data.tooltips}></i>
                        )}
                    </p>
                    {/* <p className="list_text">Hello, last date for registering for the annual music event is closing in</p> */}
                </div>
                <div className="options">
                    {props.flag && <FlagButton active={active} handleFlagUpdate={(flag) => handleFlagUpdate(flag)} />}
                    <Dropdown variant="p-0">
                        <Dropdown.Toggle variant="dropdown-toggle p-0 no-caret">
                            <i className="ti-more-alt"></i>
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                            {props.description && <Dropdown.Item><div onClick={() => props.onHandleAddDescription(props.data.id)}><i className="mdi mdi-cog me-2"></i> Project Description</div></Dropdown.Item>}
                            {props.tooltip && <Dropdown.Item><div onClick={() => props.onHandleAddTooltip(props.data.id)}><i className="mdi mdi-information me-2"></i> {props.data.tooltips ? 'Update Tooltip' : 'Add Tooltip'}</div></Dropdown.Item>}
                            {props.qr && <Dropdown.Item><div onClick={() => props.onHandleGenerateQR(props.data.id)}><i className="mdi mdi-cog me-2"></i> Generate QR </div></Dropdown.Item>}

                            <Dropdown.Item><div onClick={() => props.onHandleDelete(props.data.id)}><i className="mdi mdi-delete me-2"></i> Delete</div></Dropdown.Item>
                        </Dropdown.Menu>
                    </Dropdown>
                </div>
            </div>



        </>
    )
}

ListBoxItem.defaultProps = {
    flag: false
};

export default ListBoxItem;