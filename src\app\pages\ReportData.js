import React, { Component, useState, useMemo, useEffect } from 'react'

import $ from "jquery";
import BasicTable from '../tables/BasicTable';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import API from '../services/API';
import cogoToast from 'cogo-toast';
import { useHistory } from "react-router-dom";
import { ALL_REPORT_DATA_URL } from '../constants';
import PropTypes from 'prop-types';
import Observation from './Observation';
import CardOverlay from './CardOverlay';

import FilterLocation from './FilterLocation';
import LineChart from '../dashboard/LineChart';
import { Sticky, StickyContainer } from 'react-sticky';


import MonthlyReport from './MonthlyReport';
import { InputText } from 'primereact/inputtext';
import AllFilterLocationVertical from './AllFilterLocationVertical'
import moment from 'moment'

import { useSelector } from "react-redux";
// import ActionCard from './ActionCard';

import Typography from '@mui/material/Typography'
import { Button } from 'primereact/button';
import * as XLSX from "xlsx";

const customFontStyle = {
    fontFamily: 'Lato, sans-serif',
    display: "flex",
    alignItems: 'center',
    justifyContent: 'center'
};
function CustomTabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`observation-tabpanel-${index}`}
            aria-labelledby={`observation-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box>
                    {children}
                </Box>
            )}
        </div>
    );
}

CustomTabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `observation-tab-${index}`,
        'aria-controls': `observation-tabpanel-${index}`,
    };
}

// @ts-ignore
window.jQuery = $;
// @ts-ignore
window.$ = $;
const ReportData = () => {

    const me = useSelector((state) => state.login.user)


    const history = useHistory();
    // const isIncidentReview = useMemo(() => {
    //     return me?.validationRoles?.some(item => item.name === 'Reporter') || false;
    // }, [me]);

    // useEffect(() => {
    //     if (!isIncidentReview) {
    //         history.push('/logout')
    //     }
    // }, [me])

    const [actionData, setActionData] = useState([]);
    const [FilterActionData, setFilterActionData] = useState([]);
    const [rendered, setRendered] = useState(0)
    const [value, setValue] = useState(0);
    const [topLevelValue, setTopLevelValue] = useState(0);
    const [showFilter, setShowFilter] = useState(true)
    const [applyFilter, setApplyFilter] = useState(false)
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)

    const [filter, setFilter] = useState([])
    const [search, setSearch] = useState('')
    const [dates, setDates] = useState([])
    const [clear, setClear] = useState(true)
    const [locationOneId, setlocationOneId] = useState('')
    const [locationTwoId, setlocationTwoId] = useState('')
    const [locationThreeId, setlocationThreeId] = useState('')
    const [locationFourId, setlocationFourId] = useState('')
    const [data, setData] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const handleTopLevelChange = (event, newValue) => {
        setTopLevelValue(newValue);
    };



    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {
        setDates([])
        const data = [
            locationOneId.name || '',
            locationTwoId.name || '',
            locationThreeId.name || '',
            locationFourId.name || '',

        ];

        if (startDate !== null && endDate !== null) {
            const date = [
                moment(startDate).format('MMM YYYY'),
                moment(endDate).format('MMM YYYY')
            ]
            setDates(date)
        }


        setFilter(data)
        setSearch('')

        setlocationOneId(locationOneId.id)
        setlocationTwoId(locationTwoId.id)
        setlocationThreeId(locationThreeId.id)
        setlocationFourId(locationFourId.id)
        setStartDate(startDate)
        setEndDate(endDate)
    };

    useEffect(() => {
        const filterData1 = (data) => {
            return data.filter(item => {
                const appDetails = item.applicationDetails || {};
                return (
                    (!locationOneId || (appDetails.locationOne?.id || item.locationOneId) === locationOneId) &&
                    (!locationTwoId || (appDetails.locationTwo?.id || item.locationTwoId) === locationTwoId) &&
                    (!locationThreeId || (appDetails.locationThree?.id || item.locationThreeId) === locationThreeId) &&
                    (!locationFourId || (appDetails.locationFour?.id || item.locationFourId) === locationFourId)
                    // Uncomment and adjust the following line if date range filtering is needed
                    // && (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))
                );
            });
        };

        const filteredActionData = filterData1(FilterActionData);
        const filteredData = filterData1(filterData);

        arrangeFilter(filteredData)

        console.log(filteredData)


        setActionData(filteredActionData);
        // setData(filteredData);

    }, [locationOneId, locationTwoId, locationThreeId, locationFourId]);

    const arrangeFilter = (data) => {


        console.log(data)
        console.log("test")
        const result = {};
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        data.forEach(entry => {
            const [monthName, year] = entry.yearAndMonth.split(' ');
            const monthIndex = months.indexOf(monthName);
            const locationKey = `${entry.locationOneId}-${entry.locationTwoId}-${entry.locationThreeId}-${entry.locationFourId}-${year}`;

            if (!result[locationKey]) {
                result[locationKey] = {
                    name: `${entry.locationOne.name ?? ''} > ${entry.locationTwo.name ?? ''} > ${entry.locationThree.name ?? ''} > ${entry.locationFour.name ?? ''}`,
                    year: year,
                    data: {}
                };

                Object.keys(entry).forEach(key => {
                    if (!['locationOne', 'locationTwo', 'locationThree', 'reviewedDate', 'locationFour', 'reviewer', 'locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'userId', 'yearAndMonth', 'type', 'user', 'noOfRiskObservation', 'noOfSafeObservation', 'reviewerId'].includes(key)) {
                        result[locationKey].data[key] = months.map(() => '');
                    }
                });

                result[locationKey].data['Reported By'] = months.map(() => '');
                result[locationKey].data['Reviewed By'] = months.map(() => '');
                result[locationKey].data['Reviewed Date'] = months.map(() => '');
            }

            Object.keys(entry).forEach(key => {
                if (key in result[locationKey].data) {
                    let value = entry[key].toString();
                    result[locationKey].data[key][monthIndex] = value;
                }
            });

            if (entry.user && entry.user.firstName) {
                result[locationKey].data['Reported By'][monthIndex] = entry.user.firstName;
            }
            if (entry.reviewer && entry.reviewer.firstName) {
                result[locationKey].data['Reviewed By'][monthIndex] = entry.reviewer.firstName;
            }
            if (entry.reviewedDate) {
                result[locationKey].data['Reviewed Date'][monthIndex] = moment(entry.reviewedDate, "DD-MM-YYYY HH:mm").format("DD/MM/YYYY");
            }
        });

        const finalResult = Object.values(result).map(location => ({
            name: location.name,
            year: location.year,
            data: Object.keys(location.data).map(key => ({
                name: key,
                ...months.reduce((acc, month, idx) => {
                    acc[month.toLowerCase()] = location.data[key][idx] !== '' ? location.data[key][idx] : "-";
                    return acc;
                }, {})
            }))
        }));

        const rearrangedData = finalResult.map(project => {

            const reviewedByIndex = project.data.findIndex(entry => entry.name === 'Reviewed By');
            if (reviewedByIndex !== -1) {
                const reviewedByEntry = project.data.splice(reviewedByIndex, 1)[0];
                project.data.unshift(reviewedByEntry);
            }

            // Move Reviewed Date to top
            const reviewedDateIndex = project.data.findIndex(entry => entry.name === 'Reviewed Date');
            if (reviewedDateIndex !== -1) {
                const reviewedDateEntry = project.data.splice(reviewedDateIndex, 1)[0];
                project.data.unshift(reviewedDateEntry);
            }

            const reportedByIndex = project.data.findIndex(entry => entry.name === 'Reported By');
            if (reportedByIndex !== -1) {
                const reportedByEntry = project.data.splice(reportedByIndex, 1)[0];
                project.data.unshift(reportedByEntry);
            }
 const dateIndex = project.data.findIndex(entry => entry.name === 'date');
 if (dateIndex !== -1) {
   const dateEntry = project.data.splice(dateIndex, 1)[0];
   project.data.unshift(dateEntry);
 }

            // const dateIndex = project.data.findIndex(entry => entry.name === 'date');
            // if (reportedByIndex !== -1) {
            //     const reportedByEntry = project.data.splice(dateIndex, 1)[0];
            //     project.data.unshift(reportedByEntry);
            // }
            return project;
        });

        setData(rearrangedData)


    }

    const [totalObservation, setTotalObservation] = useState(0)
    const [totalOtherObservation, setTotalOtherObservation] = useState(0)



    const getFilteredActions = (applicationType, statusList) => {
        return actionData.filter(action =>
            action.application === applicationType && statusList.includes(action.status)
        );
    }

    useEffect(() => {
        getObservationData();
    }, [])

    const getObservationData = async () => {

        const params = {
            "include": [{ "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "user" }, { "relation": "reviewer" }]

        };
        const response = await API.get(`${ALL_REPORT_DATA_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            // setData(response.data.filter(i => i.type === 'monthly'))
            setFilterData(response.data.filter(i => i.type === 'monthly'))

            const result = {};
            const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

            response.data.forEach(entry => {
                const [monthName, year] = entry.yearAndMonth.split(' ');
                const monthIndex = months.indexOf(monthName);
                const locationKey = `${entry.locationOneId}-${entry.locationTwoId}-${entry.locationThreeId}-${entry.locationFourId}-${year}`;

                if (!result[locationKey]) {
                    result[locationKey] = {
                        name: `${entry.locationOne.name ?? ''} > ${entry.locationTwo.name ?? ''} > ${entry.locationThree.name ?? ''} > ${entry.locationFour.name ?? ''}`,
                        year: year,
                        data: {}
                    };

                    Object.keys(entry).forEach(key => {
                        if (!['locationOne', 'locationTwo', 'locationThree', 'locationFour', 'reviewedDate', 'reviewer', 'locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'userId', 'yearAndMonth', 'type', 'user', 'noOfRiskObservation', 'noOfSafeObservation', 'reviewerId'].includes(key)) {
                            result[locationKey].data[key] = months.map(() => '');
                        }
                    });

                    result[locationKey].data['Reported By'] = months.map(() => '');
                    result[locationKey].data['Reviewed By'] = months.map(() => '');
                    result[locationKey].data['Reviewed Date'] = months.map(() => '');
                }

                Object.keys(entry).forEach(key => {
                    if (key in result[locationKey].data) {
                        let value = entry[key].toString();
                        result[locationKey].data[key][monthIndex] = value;
                    }
                });

                if (entry.user && entry.user.firstName) {
                    result[locationKey].data['Reported By'][monthIndex] = entry.user.firstName;
                }

                if (entry.reviewer && entry.reviewer.firstName) {
                    result[locationKey].data['Reviewed By'][monthIndex] = entry.reviewer.firstName;
                }
                if (entry.reviewedDate) {
                    result[locationKey].data['Reviewed Date'][monthIndex] = moment(entry.reviewedDate, "DD-MM-YYYY HH:mm").format("DD/MM/YYYY");
                }
            });

            const finalResult = Object.values(result).map(location => ({
                name: location.name,
                year: location.year,
                data: Object.keys(location.data).map(key => ({
                    name: key,
                    ...months.reduce((acc, month, idx) => {
                        acc[month.toLowerCase()] = location.data[key][idx] !== '' ? location.data[key][idx] : "-";
                        return acc;
                    }, {})
                }))
            }));

            const rearrangedData = finalResult.map(project => {

                const reviewedByIndex = project.data.findIndex(entry => entry.name === 'Reviewed By');
                if (reviewedByIndex !== -1) {
                    const reviewedByEntry = project.data.splice(reviewedByIndex, 1)[0];
                    project.data.unshift(reviewedByEntry);
                }

                // Move Reviewed Date to top
                const reviewedDateIndex = project.data.findIndex(entry => entry.name === 'Reviewed Date');
                if (reviewedDateIndex !== -1) {
                    const reviewedDateEntry = project.data.splice(reviewedDateIndex, 1)[0];
                    project.data.unshift(reviewedDateEntry);
                }

                const reportedByIndex = project.data.findIndex(entry => entry.name === 'Reported By');
                if (reportedByIndex !== -1) {
                    const reportedByEntry = project.data.splice(reportedByIndex, 1)[0];
                    project.data.unshift(reportedByEntry);
                }

                const dateIndex = project.data.findIndex(entry => entry.name === 'date');
                if (dateIndex !== -1) {
                    const dateEntry = project.data.splice(dateIndex, 1)[0];
                    project.data.unshift(dateEntry);
                }
                // const dateIndex = project.data.findIndex(entry => entry.name === 'date');
                // if (reportedByIndex !== -1) {
                //     const reportedByEntry = project.data.splice(dateIndex, 1)[0];
                //     project.data.unshift(reportedByEntry);
                // }
                return project;
            });


            console.log(rearrangedData)
            console.log('in')
            setData(rearrangedData)


        }
    }

    const onApplyFilter = (type) => {
        setApplyFilter(type)
        setShowFilter(true)
    }

    const onCancelFilter = (type) => {
        setApplyFilter(false)
        setShowFilter(true)
        setClear(!clear)
    }

    const uniqueYears = [...new Set(data.map((item) => item.year))].sort();
    console.log(data)

    function exportYearWiseExcelWithProjectName(dataArray, fileName = "YearWiseData.xlsx") {
        if (!dataArray || !Array.isArray(dataArray) || dataArray.length === 0) {
            alert("No data to export");
            return;
        }

        // Create a new workbook
        const workbook = XLSX.utils.book_new();

        // 1) Group rows by year; store both the row data and the project name
        const yearMap = {};

        dataArray.forEach((item) => {
            const currentYear = item.year || "Unknown";
            if (!yearMap[currentYear]) {
                yearMap[currentYear] = [];
            }

            // For each row in item.data, push an object containing:
            // - projectName: item.name
            // - rowData: the row itself
            if (Array.isArray(item.data)) {
                item.data.forEach((row) => {
                    yearMap[currentYear].push({
                        projectName: item.name, // e.g. "Project C"
                        rowData: row,
                    });
                });
            }
        });

        // 2) Create a worksheet for each year
        Object.keys(yearMap).forEach((year) => {
            // Transform all rows for this year into JSON for SheetJS
            const transformedData = yearMap[year].map(({ projectName, rowData }) => ({
                // Add a "Project" column that shows the project name
                Project: projectName ?? "",
                // Then map the monthly data as before
                Name: rowData.name ?? "",
                Jan: rowData.jan ?? "",
                Feb: rowData.feb ?? "",
                Mar: rowData.mar ?? "",
                Apr: rowData.apr ?? "",
                May: rowData.may ?? "",
                Jun: rowData.jun ?? "",
                Jul: rowData.jul ?? "",
                Aug: rowData.aug ?? "",
                Sep: rowData.sep ?? "",
                Oct: rowData.oct ?? "",
                Nov: rowData.nov ?? "",
                Dec: rowData.dec ?? "",
            }));

            // Convert that array to a worksheet
            const worksheet = XLSX.utils.json_to_sheet(transformedData);

            // Sheet name: just use the year (truncate if it exceeds 31 chars)
            let sheetName = year;
            if (sheetName.length > 31) {
                sheetName = sheetName.substring(0, 31);
            }

            // Append this worksheet to the workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
        });

        // 3) Trigger file download
        XLSX.writeFile(workbook, fileName);
    }






    return (
        <>
            <CardOverlay>
                <StickyContainer>


                    <div className='row'>
                        <div className='col-12 mb-4'>
                            <div className='d-flex align-items-center'>
                                <div className='col-1 d-flex'>
                                    {!applyFilter ?
                                        <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                                            <div className='d-flex flex-column align-items-end'>
                                                <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                                                <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                                            </div>
                                            <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                                        </div>
                                        :
                                        <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                                            <div className='d-flex flex-column align-items-end'>
                                                <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                                                <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                                            </div>
                                            <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                                        </div>
                                    }

                                </div>
                                <div className='col-9 d-flex'>
                                    {applyFilter && <>
                                        {filter.length !== 0 &&
                                            <h5><b>Location : </b>{filter.map((location, index) => (
                                                location !== '' &&
                                                <React.Fragment key={index}>
                                                    <span className='loc-box'>{location}</span>
                                                    {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                                                </React.Fragment>
                                            ))}</h5>
                                        }
                                        {dates.length !== 0 &&
                                            <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                                                <React.Fragment key={index}>
                                                    <span className='loc-box'>{location}</span>
                                                    {index < dates.length - 1 && " To "}
                                                </React.Fragment>
                                            ))}</h5>
                                        }
                                    </>}
                                </div>
                                <div className='col-2'>

                                    {/* <Button label="Export" icon="pi pi-upload" className="p-button" onClick={() => exportYearWiseExcelWithProjectName(data, "ConstructionData.xlsx")} /> */}
                                    {/* <div className="p-input-icon-left ">
                                        <i className="fa fa-search" />

                                    </div> */}
                                </div>
                            </div>
                        </div>

                        <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>

                            <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
                        </div>

                        <div className={!showFilter ? 'col-9' : 'col-12'}>
                            <Box sx={{ width: '100%' }}>

                                <Box sx={{ width: '100%' }}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>


                                        <Tabs value={value} onChange={handleChange} aria-label="observation report table">


                                            {uniqueYears.map((year, index) => (
                                                <Tab
                                                    key={year}
                                                    label={<Typography variant="body1" style={customFontStyle}>{year}</Typography>}
                                                    {...a11yProps(index)}
                                                />
                                            ))}
                                            {/* <Tab label={`My Actions`} {...a11yProps(0)} />
                                            <Tab label={`2024`} {...a11yProps(1)} /> */}

                                        </Tabs>
                                    </Box>
                                </Box>

                                {uniqueYears.map((year, index) => (
                                    <CustomTabPanel key={year} value={value} index={index}>

                                        <MonthlyReport
                                            data={data.filter((item) => item.year === year)}
                                            clear={clear}
                                            search={search}
                                            getData={getObservationData}
                                            exportYearWiseExcelWithProjectName={exportYearWiseExcelWithProjectName}
                                        />
                                    </CustomTabPanel>
                                ))}


                            </Box>



                        </div>


                    </div>


                </StickyContainer>


            </CardOverlay >
        </>
    )
}

export default ReportData
