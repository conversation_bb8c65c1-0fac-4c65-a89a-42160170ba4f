import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Table } from 'react-bootstrap';
import API from '../services/API';
import { EPTW_CHECKLIST, EPTW_CHECKLIST_WITH_ID } from '../constants';
import CardOverlay from './CardOverlay';
import 'bootstrap/dist/css/bootstrap.min.css';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import 'primereact/resources/themes/saga-blue/theme.css'; // Choose your theme
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
const EptwChecklist = () => {
    const [checklists, setChecklists] = useState([]);
    const [applicableOptions, setApplicableOptions] = useState([]);
    const [editing, setEditing] = useState(null);
    const [newQuestion, setNewQuestion] = useState({
        label: '',
        applicable: [],
        attachment: false,
        attachText: '',
        user: false,
        userText: '',
        option: [],
    });
    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const toast = useRef(null);
    // Fetch checklist items and applicable options
    useEffect(() => {
        API.get(EPTW_CHECKLIST)
            .then((response) => setChecklists(response.data))
            .catch((error) => console.error('Error fetching checklist:', error));

        const options = [
            { id: 0, label: 'Work at Height' },
            { id: 1, label: 'Confined Space' },
            { id: 2, label: 'Energised System' },
            { id: 3, label: 'Hot Works' },
            { id: 4, label: 'Lifting Operation' },
            { id: 5, label: 'Use of Suspended PVAE' },
            { id: 6, label: 'Demolition' },
            { id: 7, label: 'Ground Disturbance' },
            { id: 8, label: 'Hoist/Mast Climber' },
            { id: 9, label: 'Penetrations, Shafts, Risers & Voids' },
            { id: 10, label: 'Piling Works' },
        ];
        setApplicableOptions(options);
    }, []);

    // Handle PATCH update
    const saveChanges = (id, updatedItem) => {
        API.patch(EPTW_CHECKLIST_WITH_ID(id), updatedItem)
            .then(() => {
                // Assume the update was successful and update local state
                setChecklists((prev) =>
                    prev.map((item) => (item.id === id ? updatedItem : item))
                );
                setEditing(null);
                setShowEditModal(false);
            })
            .catch((error) => console.error('Error saving changes:', error));
    };


    // Handle adding a new checklist item
    const addQuestion = () => {
        API.post(EPTW_CHECKLIST, newQuestion)
            .then((response) => {
                setChecklists([...checklists, response.data]);
                setNewQuestion({
                    label: '',
                    applicable: [],
                    attachment: false,
                    attachText: '',
                    user: false,
                    userText: '',
                    option: [],
                });
                setShowAddModal(false);
            })
            .catch((error) => console.error('Error adding question:', error));
    };
    // Function to handle deleting an item
    const deleteItem = (id) => {
        API.delete(EPTW_CHECKLIST_WITH_ID(id))
            .then(() => {
                setChecklists((prev) => prev.filter((item) => item.id !== id));
                toast.current.show({
                    severity: 'success',
                    summary: 'Deleted',
                    detail: 'Item deleted successfully',
                    life: 3000,
                });
            })
            .catch((error) => {
                console.error('Error deleting item:', error);
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete item',
                    life: 3000,
                });
            });
    };

    const confirmDelete = (id) => {
        confirmDialog({
            message: 'Are you sure you want to delete this item?',
            header: 'Confirm Deletion',
            icon: 'pi pi-exclamation-triangle',
            accept: () => deleteItem(id),
            reject: () => {
                toast.current.show({
                    severity: 'info',
                    summary: 'Cancelled',
                    detail: 'Delete action cancelled',
                    life: 3000,
                });
            },
        });
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <div className="d-flex">
                <Button
                    icon="fa fa-pencil"
                    className="p-button-rounded p-button-warning mr-2"
                    onClick={() => {
                        setEditing(rowData);
                        setShowEditModal(true);
                    }}
                    tooltip="Edit"
                />
                <Button
                    icon="pi pi-trash"
                    className="p-button-rounded p-button-danger"
                    onClick={() => confirmDelete(rowData.id)}
                    tooltip="Delete"
                />
            </div>
        );
    };

    return (
        <>
            <div>
                <CardOverlay>
                    <Toast ref={toast} /> {/* Toast Component for messages */}
                    <ConfirmDialog /> {/* ConfirmDialog Component for delete confirmation */}
                    <div className="container mt-4">
                        <h3 className="mb-4">Hazard Checklist</h3>
                        <div className='d-flex justify-content-end'>
                            <Button
                                variant="primary"
                                className="mt-4 mb-4"
                                onClick={() => setShowAddModal(true)}
                            >
                                Add New Question
                            </Button>
                        </div>


                        <DataTable value={checklists} paginator rows={10} className="p-mt-4">
                            <Column field="id" header="ID"></Column>
                            <Column field="label" header="Label"></Column>
                            <Column
                                field="applicable"
                                header="Applicable"
                                body={(rowData) =>
                                    rowData.applicable
                                        .map((id) => {
                                            const option = applicableOptions.find((opt) => opt.id === id);
                                            return option ? option.label : '';
                                        })
                                        .join(', ')
                                }
                            ></Column>
                            <Column
                                field="user"
                                header="User"
                                body={(rowData) => (rowData.user ? 'Yes' : 'No')}
                            ></Column>
                            <Column field="userText" header="User Text"></Column>
                            <Column
                                field="attachment"
                                header="Attachment"
                                body={(rowData) => (rowData.attachment ? 'Yes' : 'No')}
                            ></Column>
                            <Column field="attachText" header="Attach Text"></Column>
                            <Column body={actionBodyTemplate} header="Actions"></Column>
                        </DataTable>


                    </div>

                    {/* Add Question Modal */}
                    <Modal show={showAddModal} onHide={() => setShowAddModal(false)}>
                        <Modal.Header closeButton>
                            <Modal.Title>Add New Question</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                            <Form.Group className="mb-3">
                                <Form.Control
                                    type="text"
                                    placeholder="Label"
                                    value={newQuestion.label}
                                    onChange={(e) =>
                                        setNewQuestion({ ...newQuestion, label: e.target.value })
                                    }
                                />
                            </Form.Group>
                            {applicableOptions.map((option) => (
                                <Form.Check
                                    type="checkbox"
                                    label={option.label}
                                    key={option.id}
                                    checked={newQuestion.applicable.includes(option.id)}
                                    onChange={(e) => {
                                        const newApplicable = e.target.checked
                                            ? [...newQuestion.applicable, option.id]
                                            : newQuestion.applicable.filter(
                                                (id) => id !== option.id
                                            );
                                        setNewQuestion({
                                            ...newQuestion,
                                            applicable: newApplicable,
                                        });
                                    }}
                                />
                            ))}
                            <Form.Check
                                className="mt-3"
                                type="checkbox"
                                label="Attachment"
                                checked={newQuestion.attachment}
                                onChange={(e) =>
                                    setNewQuestion({
                                        ...newQuestion,
                                        attachment: e.target.checked,
                                    })
                                }
                            />
                            {newQuestion.attachment && ( // Render Attachment Text input if attachment is true
                                <Form.Control
                                    className="mt-2"
                                    type="text"
                                    placeholder="Attachment Text"
                                    value={newQuestion.attachText}
                                    onChange={(e) =>
                                        setNewQuestion({
                                            ...newQuestion,
                                            attachText: e.target.value,
                                        })
                                    }
                                />
                            )}

                            <Form.Check
                                className="mt-3"
                                type="checkbox"
                                label="User"
                                checked={newQuestion.user}
                                onChange={(e) =>
                                    setNewQuestion({ ...newQuestion, user: e.target.checked })
                                }
                            />
                            {newQuestion.user && ( // Render User Text input if user is true
                                <Form.Control
                                    className="mt-2"
                                    type="text"
                                    placeholder="User Text"
                                    value={newQuestion.userText}
                                    onChange={(e) =>
                                        setNewQuestion({
                                            ...newQuestion,
                                            userText: e.target.value,
                                        })
                                    }
                                />
                            )}

                        </Modal.Body>
                        <Modal.Footer>
                            <Button variant="primary" onClick={addQuestion}>
                                Add
                            </Button>
                            <Button variant="secondary" onClick={() => setShowAddModal(false)}>
                                Cancel
                            </Button>
                        </Modal.Footer>
                    </Modal>

                    {/* Edit Question Modal */}
                    <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>
                        <Modal.Header closeButton>
                            <Modal.Title>Edit Question</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                            <Form.Group className="mb-3">
                                <Form.Control
                                    type="text"
                                    placeholder="Label"
                                    value={editing?.label || ''}
                                    onChange={(e) =>
                                        setEditing({ ...editing, label: e.target.value })
                                    }
                                />
                            </Form.Group>
                            {applicableOptions.map((option) => (
                                <Form.Check
                                    type="checkbox"
                                    label={option.label}
                                    key={option.id}
                                    checked={editing?.applicable?.includes(option.id)}
                                    onChange={(e) => {
                                        const newApplicable = e.target.checked
                                            ? [...editing.applicable, option.id]
                                            : editing.applicable.filter((id) => id !== option.id);
                                        setEditing({
                                            ...editing,
                                            applicable: newApplicable,
                                        });
                                    }}
                                />
                            ))}
                            <Form.Check
                                className="mt-3"
                                type="checkbox"
                                label="Attachment"
                                checked={editing?.attachment || false}
                                onChange={(e) =>
                                    setEditing({
                                        ...editing,
                                        attachment: e.target.checked,
                                    })
                                }
                            />
                            {editing?.attachment && ( // Render Attachment Text input if attachment is true
                                <Form.Control
                                    className="mt-2"
                                    type="text"
                                    placeholder="Attachment Text"
                                    value={editing?.attachText || ''}
                                    onChange={(e) =>
                                        setEditing({
                                            ...editing,
                                            attachText: e.target.value,
                                        })
                                    }
                                />
                            )}

                            <Form.Check
                                className="mt-3"
                                type="checkbox"
                                label="User"
                                checked={editing?.user || false}
                                onChange={(e) =>
                                    setEditing({ ...editing, user: e.target.checked })
                                }
                            />
                            {editing?.user && ( // Render User Text input if user is true
                                <Form.Control
                                    className="mt-2"
                                    type="text"
                                    placeholder="User Text"
                                    value={editing?.userText || ''}
                                    onChange={(e) =>
                                        setEditing({
                                            ...editing,
                                            userText: e.target.value,
                                        })
                                    }
                                />
                            )}

                        </Modal.Body>
                        <Modal.Footer>
                            <Button variant="primary" onClick={() => saveChanges(editing.id, editing)}>
                                Save
                            </Button>
                            <Button variant="secondary" onClick={() => setShowEditModal(false)}>
                                Cancel
                            </Button>
                        </Modal.Footer>
                    </Modal>
                </CardOverlay>
            </div>
        </>
    );
};

export default EptwChecklist;
